module "ecs" {
  source = "../../services/ecs"

  app_name          = "monapi"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/mono-api:${var.image_tag}"
  cpu               = 2048
  memory            = 4096
  desired_count     = 1
  container_port    = 3000
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.environment
  env = [
    {
      "name" : "NODE_ENV",
      "value" : var.environment
    },
    {
      "name" : "TZ",
      "value" : "America/Mexico_City"
    },
    {
      "name" : "DD_ENV",
      "value" : var.environment
    },
    {
      "name" : "DD_SERVICE",
      "value" : "monapi"
    },
    {
      "name" : "DD_VERSION",
      "value" : "1.3.1"
    },
    {
      "name" : "DD_LOGS_INJECTION",
      "value" : "true"
    },
    {
      "name" : "DD_AGENT_HOST",
      "value" : "dd-ingest.propaga.io"
    },
    {
      "name" : "DD_APPSEC_ENABLED",
      "value" : "true"
    }
  ]
}
