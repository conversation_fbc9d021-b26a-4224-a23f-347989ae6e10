variable "app_name" {
  type        = string
  description = "App name"
}

variable "image_repo_url" {
  type        = string
  description = "ECR image url"
}

variable "cpu" {
  type        = number
  description = "CPU reserved to image"
}

variable "memory" {
  type        = number
  description = "Memory reserved to use ECS"
}

variable "desired_count" {
  type        = number
  description = "Desired instance number"
}

variable "container_port" {
  type        = number
  description = "Container port"
}

variable "vpc_id" {
  type        = string
  description = "VPC id"
}

variable "cluster_id" {
  type        = string
  description = "Cluster id"
}

variable "security_group_id" {
  type        = string
  description = "Security group id"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets id list"
}

variable "load_balancer_arn" {
  type        = string
  description = "Load balancer arn"
}

variable "env" {
  type        = list(any)
  description = "Environment variables"
  default     = []
}

variable "Environment" {
  description = "Environment variables"
  type        = string
}

