output "ecs_service_url" {
  description = "ECS url service"
  value       = aws_lb_target_group.target_group.arn
}

output "ecs_task_definition_arn" {
  description = "ARN task definition"
  value       = aws_ecs_task_definition.task_definition.arn
}

output "target_group_arn" {
  description = "Target group ARN"
  value       = aws_lb_target_group.target_group.arn
}

output "ecs_service_name" {
  description = "ECS service name"
  value       = aws_ecs_service.service.name
}

output "ecs_cluster_name" {
  description = "ECS cluster name"
  value       = var.cluster_id
}

output "autoscaling_target_arn" {
  description = "Auto-scaling target ARN"
  value       = var.enable_autoscaling ? aws_appautoscaling_target.ecs_target[0].arn : null
}

output "cpu_policy_arn" {
  description = "CPU scaling policy ARN"
  value       = var.enable_autoscaling ? aws_appautoscaling_policy.ecs_cpu_policy[0].arn : null
}

output "memory_policy_arn" {
  description = "Memory scaling policy ARN"
  value       = var.enable_autoscaling ? aws_appautoscaling_policy.ecs_memory_policy[0].arn : null
}