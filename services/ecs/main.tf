data "aws_iam_policy_document" "ecs_tasks_execution_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "ecs_tasks_execution_role" {
  name               = "${var.app_name}-ecs-task-execution-role-${var.Environment}"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_execution_role.json
}

resource "aws_iam_role_policy_attachment" "ecs_tasks_execution_role" {
  role       = aws_iam_role.ecs_tasks_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "secret_manager_policy" {
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
  role       = aws_iam_role.ecs_tasks_execution_role.name
}

resource "aws_iam_role_policy_attachment" "s3_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
  role       = aws_iam_role.ecs_tasks_execution_role.name
}

resource "aws_iam_role_policy_attachment" "app_config_policy" {
  policy_arn = "arn:aws:iam::014817073571:policy/AppConfigFeatureFlag"
  role       = aws_iam_role.ecs_tasks_execution_role.name
}

resource "aws_iam_role_policy_attachment" "bedrock_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonBedrockFullAccess"
  role       = aws_iam_role.ecs_tasks_execution_role.name
}

resource "aws_lb_target_group" "target_group" {
  name_prefix = var.app_name
  port        = var.container_port
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.vpc_id

  health_check {
    healthy_threshold   = 2
    interval            = 30
    path                = "/"
    port                = var.container_port
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
    matcher             = "200-499"
  }
}

resource "aws_alb_listener" "alb_listener" {
  load_balancer_arn = var.load_balancer_arn

  port     = var.container_port
  protocol = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.target_group.arn
  }
}

resource "aws_ecs_task_definition" "task_definition" {
  network_mode             = "awsvpc"
  family                   = var.app_name
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.cpu
  memory                   = var.memory
  execution_role_arn       = aws_iam_role.ecs_tasks_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_tasks_execution_role.arn

  container_definitions = jsonencode([{
    name        = var.app_name
    image       = var.image_repo_url
    essential   = true
    environment = var.env
    portMappings = [{
      containerPort = var.container_port
      hostPort      = var.container_port
    }]
    logConfiguration = {
      logDriver = "awslogs",
      options = {
        "awslogs-group"         = var.app_name
        "awslogs-stream-prefix" = var.Environment
        "awslogs-region"        = "us-west-2"
      }
    }
  }])
}

resource "aws_ecs_service" "service" {
  name                 = var.app_name
  cluster              = var.cluster_id
  task_definition      = aws_ecs_task_definition.task_definition.arn
  desired_count        = var.desired_count
  launch_type          = "FARGATE"
  force_new_deployment = true

  network_configuration {
    security_groups  = [var.security_group_id]
    assign_public_ip = true
    subnets          = var.subnets
  }

  deployment_controller {
    type = "ECS"
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.target_group.arn
    container_name   = var.app_name
    container_port   = var.container_port
  }
}

